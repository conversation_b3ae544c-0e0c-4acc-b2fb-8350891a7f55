// Game state
let gameState = {
    playerScore: 0,
    computerScore: 0,
    currentRound: 1,
    maxRounds: 5,
    gameOver: false
};

// Choice mappings
const choices = {
    rock: { icon: '🪨', name: 'Rock' },
    paper: { icon: '📄', name: 'Paper' },
    scissors: { icon: '✂️', name: 'Scissors' }
};

// DOM elements
const choiceButtons = document.querySelectorAll('.choice-btn');
const battleSection = document.getElementById('battle-section');
const gameOverSection = document.getElementById('game-over-section');
const currentRoundElement = document.getElementById('current-round');
const playerScoreElement = document.getElementById('player-score');
const computerScoreElement = document.getElementById('computer-score');
const playerChoiceIcon = document.getElementById('player-choice-icon');
const playerChoiceText = document.getElementById('player-choice-text');
const computerChoiceIcon = document.getElementById('computer-choice-icon');
const computerChoiceText = document.getElementById('computer-choice-text');
const resultMessage = document.getElementById('result-message');
const finalMessage = document.getElementById('final-message');
const finalPlayerScore = document.getElementById('final-player-score');
const finalComputerScore = document.getElementById('final-computer-score');
const playAgainBtn = document.getElementById('play-again-btn');

// Initialize game
function initGame() {
    // Add event listeners to choice buttons
    choiceButtons.forEach(button => {
        button.addEventListener('click', handlePlayerChoice);
    });

    // Add event listener to play again button
    playAgainBtn.addEventListener('click', resetGame);

    // Update display
    updateDisplay();
}

// Handle player choice
function handlePlayerChoice(event) {
    if (gameState.gameOver) return;

    const playerChoice = event.currentTarget.dataset.choice;
    const computerChoice = getComputerChoice();
    
    // Play the round
    playRound(playerChoice, computerChoice);
}

// Get random computer choice
function getComputerChoice() {
    const choiceKeys = Object.keys(choices);
    const randomIndex = Math.floor(Math.random() * choiceKeys.length);
    return choiceKeys[randomIndex];
}

// Play a single round
function playRound(playerChoice, computerChoice) {
    // Show battle section
    battleSection.style.display = 'block';
    battleSection.classList.add('fade-in');

    // Update choice displays
    updateChoiceDisplay(playerChoice, computerChoice);

    // Determine winner
    const roundResult = determineWinner(playerChoice, computerChoice);
    
    // Update scores
    if (roundResult === 'player') {
        gameState.playerScore++;
    } else if (roundResult === 'computer') {
        gameState.computerScore++;
    }

    // Show round result
    showRoundResult(roundResult);

    // Update display
    updateDisplay();

    // Check if game is over
    if (gameState.currentRound >= gameState.maxRounds) {
        setTimeout(() => {
            endGame();
        }, 2000);
    } else {
        // Prepare for next round
        gameState.currentRound++;
        setTimeout(() => {
            battleSection.style.display = 'none';
            battleSection.classList.remove('fade-in');
        }, 3000);
    }
}

// Determine round winner
function determineWinner(playerChoice, computerChoice) {
    if (playerChoice === computerChoice) {
        return 'tie';
    }

    const winConditions = {
        rock: 'scissors',
        paper: 'rock',
        scissors: 'paper'
    };

    if (winConditions[playerChoice] === computerChoice) {
        return 'player';
    } else {
        return 'computer';
    }
}

// Update choice display in battle section
function updateChoiceDisplay(playerChoice, computerChoice) {
    // Player choice
    playerChoiceIcon.textContent = choices[playerChoice].icon;
    playerChoiceText.textContent = choices[playerChoice].name;

    // Computer choice
    computerChoiceIcon.textContent = choices[computerChoice].icon;
    computerChoiceText.textContent = choices[computerChoice].name;
}

// Show round result
function showRoundResult(result) {
    const resultElement = document.getElementById('round-result');
    resultElement.classList.add('pulse');

    // Remove previous result classes
    resultMessage.classList.remove('win', 'lose', 'tie');

    switch (result) {
        case 'player':
            resultMessage.textContent = '🎉 You Win This Round!';
            resultMessage.classList.add('win');
            break;
        case 'computer':
            resultMessage.textContent = '💻 Computer Wins This Round!';
            resultMessage.classList.add('lose');
            break;
        case 'tie':
            resultMessage.textContent = '🤝 It\'s a Tie!';
            resultMessage.classList.add('tie');
            break;
    }

    // Remove animation class after animation completes
    setTimeout(() => {
        resultElement.classList.remove('pulse');
    }, 600);
}

// Update display elements
function updateDisplay() {
    currentRoundElement.textContent = gameState.currentRound;
    playerScoreElement.textContent = gameState.playerScore;
    computerScoreElement.textContent = gameState.computerScore;
}

// End the game
function endGame() {
    gameState.gameOver = true;
    battleSection.style.display = 'none';
    gameOverSection.style.display = 'block';
    gameOverSection.classList.add('fade-in');

    // Update final scores
    finalPlayerScore.textContent = gameState.playerScore;
    finalComputerScore.textContent = gameState.computerScore;

    // Determine final winner and show message
    const finalMessageElement = document.getElementById('final-message');
    finalMessageElement.classList.remove('win', 'lose', 'tie');

    if (gameState.playerScore > gameState.computerScore) {
        finalMessageElement.textContent = '🏆 Congratulations! You Won The Game!';
        finalMessageElement.classList.add('win');
    } else if (gameState.computerScore > gameState.playerScore) {
        finalMessageElement.textContent = '💻 Game Over! Computer Wins The Game!';
        finalMessageElement.classList.add('lose');
    } else {
        finalMessageElement.textContent = '🤝 It\'s a Tie Game! Try Again!';
        finalMessageElement.classList.add('tie');
    }
}

// Reset game
function resetGame() {
    // Reset game state
    gameState = {
        playerScore: 0,
        computerScore: 0,
        currentRound: 1,
        maxRounds: 5,
        gameOver: false
    };

    // Hide sections
    battleSection.style.display = 'none';
    gameOverSection.style.display = 'none';

    // Remove animation classes
    battleSection.classList.remove('fade-in');
    gameOverSection.classList.remove('fade-in');

    // Update display
    updateDisplay();
}

// Initialize the game when DOM is loaded
document.addEventListener('DOMContentLoaded', initGame);
