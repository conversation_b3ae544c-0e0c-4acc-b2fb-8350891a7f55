* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 30px;
    max-width: 600px;
    width: 90%;
    text-align: center;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #4a5568;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 10px;
}

.round-counter {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2d3748;
}

.score-board {
    display: flex;
    gap: 30px;
}

.score {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.score .label {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 5px;
}

.score .value {
    font-size: 2rem;
    font-weight: bold;
    color: #2d3748;
}

.choices-section h2 {
    margin-bottom: 25px;
    color: #4a5568;
    font-size: 1.5rem;
}

.choice-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.choice-btn {
    background: white;
    border: 3px solid #e2e8f0;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 120px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.choice-btn:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.choice-btn:active {
    transform: translateY(-2px);
}

.choice-icon {
    font-size: 3rem;
    margin-bottom: 5px;
}

.choice-btn span {
    font-size: 1.1rem;
    font-weight: bold;
    color: #4a5568;
}

.battle-section {
    margin: 30px 0;
    padding: 25px;
    background: #f7fafc;
    border-radius: 15px;
}

.battle-display {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 25px;
}

.player-choice, .computer-choice {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.player-choice h3, .computer-choice h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 1.2rem;
}

.choice-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-width: 100px;
}

.choice-display .choice-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.choice-display span {
    font-weight: bold;
    color: #4a5568;
}

.vs-section {
    display: flex;
    align-items: center;
    justify-content: center;
}

.vs-text {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
    background: white;
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.round-result {
    text-align: center;
}

.round-result h2 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 10px;
    font-weight: bold;
}

.win {
    background: #c6f6d5;
    color: #22543d;
}

.lose {
    background: #fed7d7;
    color: #742a2a;
}

.tie {
    background: #fef5e7;
    color: #744210;
}

.game-over-section {
    padding: 30px;
    background: #f7fafc;
    border-radius: 15px;
    margin-top: 20px;
}

.final-result h2 {
    font-size: 2.2rem;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 10px;
    font-weight: bold;
}

.final-score {
    font-size: 1.3rem;
    margin-bottom: 25px;
    color: #4a5568;
}

.play-again-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.play-again-btn:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.play-again-btn:active {
    transform: translateY(0);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 0.6s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-container {
        padding: 20px;
        margin: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .choice-buttons {
        gap: 15px;
    }
    
    .choice-btn {
        min-width: 100px;
        padding: 15px;
    }
    
    .choice-icon {
        font-size: 2.5rem;
    }
    
    .battle-display {
        flex-direction: column;
        gap: 20px;
    }
    
    .vs-section {
        order: 2;
    }
    
    .game-info {
        flex-direction: column;
        gap: 15px;
    }
}
