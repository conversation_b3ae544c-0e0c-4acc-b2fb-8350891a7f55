* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-shape {
    position: absolute;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

.shape-5 {
    width: 90px;
    height: 90px;
    bottom: 20%;
    right: 40%;
    animation-delay: 3s;
}

/* Game Container */
.game-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    padding: 40px;
    max-width: 900px;
    width: 95%;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.game-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

/* Game Controls */
.game-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 10;
}

.control-btn {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 10px 15px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.control-btn .btn-icon {
    font-size: 1.1rem;
}

.sound-btn.muted .btn-icon {
    opacity: 0.5;
}

.sound-btn.muted .btn-text::after {
    content: ' (Off)';
}

/* Header Styles */
.game-title {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.title-icon {
    font-size: 4rem;
    animation: rotate 4s linear infinite;
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.5));
}

.title-text {
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
    letter-spacing: 3px;
}

.title-subtitle {
    font-size: 1.2rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 2px;
    text-transform: uppercase;
}

/* Game Info */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding: 25px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.round-counter {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.counter-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 1px;
}

.counter-value {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.counter-separator {
    color: rgba(255, 255, 255, 0.6);
    margin: 0 5px;
}

.progress-bar {
    width: 100px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    border-radius: 3px;
    transition: width 0.5s ease;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.score-board {
    display: flex;
    align-items: center;
    gap: 40px;
}

.score {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    position: relative;
}

.score-avatar {
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.score-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.score-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 1px;
    margin-bottom: 5px;
}

.score-value {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.vs-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.vs-text {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    padding: 10px 20px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    backdrop-filter: blur(10px);
}

.vs-lightning {
    font-size: 1.5rem;
    animation: flash 2s ease-in-out infinite;
}

/* Choices Section */
.section-title {
    margin-bottom: 40px;
    text-align: center;
    position: relative;
}

.title-glow {
    font-size: 2rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    letter-spacing: 2px;
}

.title-underline {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ffd700, transparent);
    margin: 15px auto 0;
    border-radius: 2px;
    animation: pulse 2s ease-in-out infinite;
}

.choice-buttons {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.choice-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.4s ease;
    position: relative;
    transform-style: preserve-3d;
}

.choice-btn:hover {
    transform: translateY(-10px) rotateY(5deg);
}

.choice-btn:active {
    transform: translateY(-5px) scale(0.95);
}

.choice-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 30px 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 180px;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.choice-btn:hover .choice-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
    border-color: #ffd700;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(255, 215, 0, 0.3);
}

.choice-icon {
    font-size: 4rem;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
}

.choice-btn:hover .choice-icon {
    transform: scale(1.1) rotateZ(5deg);
    filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.6));
}

.choice-name {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 700;
    color: #fff;
    letter-spacing: 2px;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.choice-power {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-style: italic;
}

.choice-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 215, 0, 0.2), transparent 70%);
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.choice-btn:hover .choice-glow {
    opacity: 1;
}

.choice-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    border-radius: 20px;
}

.choice-particles::before,
.choice-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
}

.choice-particles::before {
    top: 20%;
    left: 20%;
    animation: sparkle 2s ease-in-out infinite;
}

.choice-particles::after {
    bottom: 20%;
    right: 20%;
    animation: sparkle 2s ease-in-out infinite 1s;
}

.choice-btn:hover .choice-particles::before,
.choice-btn:hover .choice-particles::after {
    opacity: 1;
}

/* Battle Section */
.battle-section {
    margin: 40px 0;
    padding: 40px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.battle-arena {
    position: relative;
    z-index: 2;
}

.arena-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.energy-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.2), transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: energyPulse 3s ease-in-out infinite;
}

.battle-sparks {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #ffd700, transparent),
        radial-gradient(2px 2px at 40px 70px, #fff, transparent),
        radial-gradient(1px 1px at 90px 40px, #ffd700, transparent),
        radial-gradient(1px 1px at 130px 80px, #fff, transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: sparkleMove 4s linear infinite;
    opacity: 0.6;
}

.battle-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.combatant {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.combatant-header {
    margin-bottom: 20px;
    text-align: center;
}

.combatant-title {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffd700;
    letter-spacing: 2px;
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.health-bar {
    width: 120px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.health-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.5s ease;
}

.player-health {
    background: linear-gradient(90deg, #00ff88, #00cc6a);
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.computer-health {
    background: linear-gradient(90deg, #ff4757, #ff3742);
    box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

.choice-display {
    position: relative;
}

.choice-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    min-width: 150px;
    position: relative;
    overflow: hidden;
}

.choice-container .choice-icon {
    font-size: 4rem;
    margin-bottom: 15px;
    animation: battleFloat 2s ease-in-out infinite;
    filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.5));
}

.choice-name {
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    color: #fff;
    letter-spacing: 1px;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.choice-aura {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.player-aura {
    background: radial-gradient(circle at center, rgba(0, 255, 136, 0.3), transparent 70%);
    animation: auraGlow 2s ease-in-out infinite;
}

.computer-aura {
    background: radial-gradient(circle at center, rgba(255, 71, 87, 0.3), transparent 70%);
    animation: auraGlow 2s ease-in-out infinite 1s;
}

.battle-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 0 0 200px;
    position: relative;
}

.vs-section {
    position: relative;
    z-index: 3;
}

.vs-circle {
    width: 100px;
    height: 100px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(15px);
    position: relative;
    animation: vsRotate 4s linear infinite;
}

.vs-text {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    letter-spacing: 2px;
}

.vs-pulse {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(255, 215, 0, 0.5);
    border-radius: 50%;
    animation: vsPulse 2s ease-in-out infinite;
}

.lightning-bolt {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    opacity: 0;
    animation: lightning 3s ease-in-out infinite;
}

.lightning-bolt::before {
    content: '⚡';
    color: #ffd700;
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
}

.impact-zone {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50px;
    height: 50px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
}

/* Round Result */
.round-result {
    text-align: center;
    position: relative;
    z-index: 3;
    margin-top: 30px;
}

.result-explosion {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3), transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
}

.result-message {
    font-family: 'Orbitron', monospace;
    font-size: 2.2rem;
    font-weight: 900;
    margin-bottom: 20px;
    padding: 25px;
    border-radius: 15px;
    letter-spacing: 2px;
    text-transform: uppercase;
    position: relative;
    z-index: 2;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
}

.result-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.win {
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.2), rgba(0, 204, 106, 0.1));
    border: 2px solid rgba(0, 255, 136, 0.5);
    color: #00ff88;
    box-shadow:
        0 0 30px rgba(0, 255, 136, 0.3),
        inset 0 0 30px rgba(0, 255, 136, 0.1);
}

.lose {
    background: linear-gradient(145deg, rgba(255, 71, 87, 0.2), rgba(255, 55, 66, 0.1));
    border: 2px solid rgba(255, 71, 87, 0.5);
    color: #ff4757;
    box-shadow:
        0 0 30px rgba(255, 71, 87, 0.3),
        inset 0 0 30px rgba(255, 71, 87, 0.1);
}

.tie {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.2), rgba(255, 237, 78, 0.1));
    border: 2px solid rgba(255, 215, 0, 0.5);
    color: #ffd700;
    box-shadow:
        0 0 30px rgba(255, 215, 0, 0.3),
        inset 0 0 30px rgba(255, 215, 0, 0.1);
}

/* Game Over Section */
.game-over-section {
    padding: 40px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    backdrop-filter: blur(15px);
    margin-top: 30px;
    position: relative;
    overflow: hidden;
}

.victory-screen {
    position: relative;
    z-index: 2;
}

.victory-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.fireworks {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(3px 3px at 20px 30px, #ffd700, transparent),
        radial-gradient(3px 3px at 40px 70px, #ff4757, transparent),
        radial-gradient(2px 2px at 90px 40px, #00ff88, transparent),
        radial-gradient(2px 2px at 130px 80px, #ffd700, transparent);
    background-repeat: repeat;
    background-size: 200px 150px;
    animation: fireworksMove 3s linear infinite;
    opacity: 0;
}

.confetti {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(45deg, #ffd700 25%, transparent 25%),
        linear-gradient(-45deg, #ff4757 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #00ff88 75%),
        linear-gradient(-45deg, transparent 75%, #ffd700 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    animation: confettiDrop 4s linear infinite;
    opacity: 0;
}

.victory-rays {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: conic-gradient(from 0deg, transparent, rgba(255, 215, 0, 0.2), transparent, rgba(255, 215, 0, 0.2), transparent);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: rayRotate 8s linear infinite;
    opacity: 0;
}

.trophy-container {
    margin-bottom: 30px;
    position: relative;
}

.trophy {
    font-size: 5rem;
    animation: trophyBounce 2s ease-in-out infinite;
    filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.6));
}

.final-message {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 30px;
    padding: 25px;
    border-radius: 15px;
    letter-spacing: 2px;
    text-transform: uppercase;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    position: relative;
    z-index: 2;
}

.final-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    margin-bottom: 40px;
}

.stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    min-width: 120px;
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.3));
}

.stat-value {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 1px;
    text-transform: uppercase;
}

.stat-divider {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.6);
}

.game-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 15px 30px;
    color: #fff;
    cursor: pointer;
    transition: all 0.4s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    letter-spacing: 1px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    min-width: 160px;
    justify-content: center;
}

.action-btn:hover {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    border-color: #ffd700;
    transform: translateY(-3px);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(255, 215, 0, 0.3);
}

.action-btn:active {
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-size: 1rem;
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover .btn-shine {
    left: 100%;
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes flash {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes energyPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.6;
    }
}

@keyframes sparkleMove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 150px 100px;
    }
}

@keyframes battleFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes auraGlow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

@keyframes vsRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes vsPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes lightning {
    0%, 90%, 100% {
        opacity: 0;
    }
    5%, 10% {
        opacity: 1;
    }
}

@keyframes fireworksMove {
    0% {
        background-position: 0 0;
        opacity: 0;
    }
    10%, 90% {
        opacity: 1;
    }
    100% {
        background-position: 200px 150px;
        opacity: 0;
    }
}

@keyframes confettiDrop {
    0% {
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        opacity: 0;
    }
    10%, 90% {
        opacity: 0.8;
    }
    100% {
        background-position: 0 100px, 0 110px, 10px 90px, -10px 100px;
        opacity: 0;
    }
}

@keyframes rayRotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes trophyBounce {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes impactBlast {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

.impact-blast {
    animation: impactBlast 0.5s ease-out;
}

@keyframes resultExplosion {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

.result-explosion-active {
    animation: resultExplosion 0.8s ease-out;
}

.victory-active .fireworks {
    opacity: 1;
}

.victory-active .confetti {
    opacity: 1;
}

.victory-active .victory-rays {
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .game-container {
        max-width: 800px;
        padding: 35px;
    }

    .choice-buttons {
        gap: 25px;
    }

    .choice-card {
        min-width: 160px;
        padding: 25px 20px;
    }

    .battle-display {
        gap: 20px;
    }

    .battle-center {
        flex: 0 0 150px;
    }
}

@media (max-width: 768px) {
    .game-container {
        padding: 25px;
        margin: 15px;
        max-width: 95%;
    }

    .game-controls {
        top: 15px;
        right: 15px;
        gap: 8px;
    }

    .control-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .game-title {
        font-size: 2.2rem;
        margin-bottom: 25px;
    }

    .title-icon {
        font-size: 3rem;
    }

    .title-subtitle {
        font-size: 1rem;
    }

    .game-info {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }

    .score-board {
        gap: 25px;
    }

    .score {
        padding: 12px 15px;
    }

    .score-value {
        font-size: 2rem;
    }

    .choice-buttons {
        gap: 15px;
        flex-direction: column;
        align-items: center;
    }

    .choice-card {
        min-width: 200px;
        padding: 25px;
    }

    .choice-icon {
        font-size: 3.5rem;
    }

    .choice-name {
        font-size: 1.2rem;
    }

    .battle-display {
        flex-direction: column;
        gap: 25px;
    }

    .battle-center {
        order: 2;
        flex: 0 0 auto;
    }

    .vs-circle {
        width: 80px;
        height: 80px;
    }

    .vs-text {
        font-size: 1.2rem;
    }

    .choice-container {
        min-width: 120px;
        padding: 20px;
    }

    .choice-container .choice-icon {
        font-size: 3rem;
    }

    .result-message {
        font-size: 1.8rem;
        padding: 20px;
    }

    .final-message {
        font-size: 2rem;
        padding: 20px;
    }

    .final-stats {
        flex-direction: column;
        gap: 20px;
    }

    .stat-card {
        min-width: 100px;
        padding: 15px;
    }

    .stat-value {
        font-size: 2.5rem;
    }

    .game-actions {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .action-btn {
        min-width: 200px;
        padding: 12px 25px;
    }
}

@media (max-width: 480px) {
    .game-container {
        padding: 20px;
        margin: 10px;
    }

    .game-title {
        font-size: 1.8rem;
    }

    .title-icon {
        font-size: 2.5rem;
    }

    .choice-card {
        min-width: 160px;
        padding: 20px;
    }

    .choice-icon {
        font-size: 3rem;
    }

    .choice-name {
        font-size: 1rem;
    }

    .vs-circle {
        width: 60px;
        height: 60px;
    }

    .vs-text {
        font-size: 1rem;
    }

    .result-message {
        font-size: 1.5rem;
        padding: 15px;
    }

    .final-message {
        font-size: 1.6rem;
        padding: 15px;
    }

    .trophy {
        font-size: 4rem;
    }

    .stat-value {
        font-size: 2rem;
    }

    .action-btn {
        min-width: 180px;
        padding: 10px 20px;
    }

    .btn-text {
        font-size: 0.9rem;
    }
}
