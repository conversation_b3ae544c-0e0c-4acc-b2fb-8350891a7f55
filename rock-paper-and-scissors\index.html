<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rock Paper Scissors - Ultimate Edition</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="background-animation">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
        <div class="floating-shape shape-4"></div>
        <div class="floating-shape shape-5"></div>
    </div>

    <div class="game-container">
        <div class="game-controls">
            <button class="control-btn reset-btn" id="reset-btn">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">Reset</span>
            </button>
            <button class="control-btn sound-btn" id="sound-btn">
                <span class="btn-icon">🔊</span>
                <span class="btn-text">Sound</span>
            </button>
        </div>

        <header>
            <h1 class="game-title">
                <span class="title-icon">⚔️</span>
                <span class="title-text">ROCK PAPER SCISSORS</span>
                <span class="title-subtitle">Ultimate Battle</span>
            </h1>

            <div class="game-info">
                <div class="round-counter">
                    <div class="counter-label">ROUND</div>
                    <div class="counter-value">
                        <span id="current-round">1</span>
                        <span class="counter-separator">/</span>
                        <span>5</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                </div>

                <div class="score-board">
                    <div class="score player-score">
                        <div class="score-avatar">👤</div>
                        <div class="score-info">
                            <span class="score-label">PLAYER</span>
                            <span class="score-value" id="player-score">0</span>
                        </div>
                        <div class="score-streak" id="player-streak"></div>
                    </div>

                    <div class="vs-divider">
                        <span class="vs-text">VS</span>
                        <div class="vs-lightning">⚡</div>
                    </div>

                    <div class="score computer-score">
                        <div class="score-avatar">🤖</div>
                        <div class="score-info">
                            <span class="score-label">COMPUTER</span>
                            <span class="score-value" id="computer-score">0</span>
                        </div>
                        <div class="score-streak" id="computer-streak"></div>
                    </div>
                </div>
            </div>
        </header>

        <main class="game-area">
            <div class="choices-section">
                <h2 class="section-title">
                    <span class="title-glow">Choose Your Weapon</span>
                    <div class="title-underline"></div>
                </h2>

                <div class="choice-buttons">
                    <button class="choice-btn" data-choice="rock">
                        <div class="choice-card">
                            <div class="choice-icon">🪨</div>
                            <div class="choice-name">ROCK</div>
                            <div class="choice-power">Crushes Scissors</div>
                            <div class="choice-particles"></div>
                        </div>
                        <div class="choice-glow"></div>
                    </button>

                    <button class="choice-btn" data-choice="paper">
                        <div class="choice-card">
                            <div class="choice-icon">📄</div>
                            <div class="choice-name">PAPER</div>
                            <div class="choice-power">Covers Rock</div>
                            <div class="choice-particles"></div>
                        </div>
                        <div class="choice-glow"></div>
                    </button>

                    <button class="choice-btn" data-choice="scissors">
                        <div class="choice-card">
                            <div class="choice-icon">✂️</div>
                            <div class="choice-name">SCISSORS</div>
                            <div class="choice-power">Cuts Paper</div>
                            <div class="choice-particles"></div>
                        </div>
                        <div class="choice-glow"></div>
                    </button>
                </div>
            </div>

            <div class="battle-section" id="battle-section" style="display: none;">
                <div class="battle-arena">
                    <div class="arena-background">
                        <div class="energy-wave"></div>
                        <div class="battle-sparks"></div>
                    </div>

                    <div class="battle-display">
                        <div class="combatant player-combatant">
                            <div class="combatant-header">
                                <h3 class="combatant-title">PLAYER</h3>
                                <div class="health-bar">
                                    <div class="health-fill player-health"></div>
                                </div>
                            </div>
                            <div class="choice-display" id="player-choice-display">
                                <div class="choice-container">
                                    <div class="choice-icon" id="player-choice-icon"></div>
                                    <div class="choice-name" id="player-choice-text"></div>
                                    <div class="choice-aura player-aura"></div>
                                </div>
                            </div>
                        </div>

                        <div class="battle-center">
                            <div class="vs-section">
                                <div class="vs-circle">
                                    <span class="vs-text">VS</span>
                                    <div class="vs-pulse"></div>
                                </div>
                                <div class="lightning-bolt"></div>
                            </div>
                            <div class="impact-zone" id="impact-zone"></div>
                        </div>

                        <div class="combatant computer-combatant">
                            <div class="combatant-header">
                                <h3 class="combatant-title">COMPUTER</h3>
                                <div class="health-bar">
                                    <div class="health-fill computer-health"></div>
                                </div>
                            </div>
                            <div class="choice-display" id="computer-choice-display">
                                <div class="choice-container">
                                    <div class="choice-icon" id="computer-choice-icon"></div>
                                    <div class="choice-name" id="computer-choice-text"></div>
                                    <div class="choice-aura computer-aura"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="round-result" id="round-result">
                        <div class="result-explosion"></div>
                        <h2 class="result-message" id="result-message"></h2>
                        <div class="result-effects"></div>
                    </div>
                </div>
            </div>

            <div class="game-over-section" id="game-over-section" style="display: none;">
                <div class="victory-screen">
                    <div class="victory-background">
                        <div class="fireworks"></div>
                        <div class="confetti"></div>
                        <div class="victory-rays"></div>
                    </div>

                    <div class="final-result">
                        <div class="trophy-container">
                            <div class="trophy" id="trophy"></div>
                        </div>

                        <h2 class="final-message" id="final-message"></h2>

                        <div class="final-stats">
                            <div class="stat-card">
                                <div class="stat-icon">👤</div>
                                <div class="stat-value" id="final-player-score">0</div>
                                <div class="stat-label">Player</div>
                            </div>

                            <div class="stat-divider">-</div>

                            <div class="stat-card">
                                <div class="stat-icon">🤖</div>
                                <div class="stat-value" id="final-computer-score">0</div>
                                <div class="stat-label">Computer</div>
                            </div>
                        </div>

                        <div class="game-actions">
                            <button class="action-btn play-again-btn" id="play-again-btn">
                                <span class="btn-icon">🎮</span>
                                <span class="btn-text">PLAY AGAIN</span>
                                <div class="btn-shine"></div>
                            </button>

                            <button class="action-btn new-game-btn" id="new-game-btn">
                                <span class="btn-icon">✨</span>
                                <span class="btn-text">NEW GAME</span>
                                <div class="btn-shine"></div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Sound Effects -->
    <audio id="click-sound" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>

    <audio id="win-sound" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>

    <script src="script.js"></script>
</body>
</html>
